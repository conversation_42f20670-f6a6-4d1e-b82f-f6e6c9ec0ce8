// ** Next.js API Route - Individual Email Template Operations
import jwt from 'jsonwebtoken'

const jwtConfig = {
  secret: process.env.NEXT_PUBLIC_JWT_SECRET || 'your-secret-key'
}

// ** Auth middleware
const authenticateToken = (req) => {
  const token = req.headers.authorization
  if (!token) throw new Error('No token provided')
  
  return jwt.verify(token, jwtConfig.secret)
}

// ** Import templates from main file (in production, use database)
// This is a simplified approach - in production you'd use a shared database
let emailTemplates = [
  {
    id: 'welcome',
    name: 'Welcome Email',
    description: 'Welcome new customers to your service',
    category: 'onboarding',
    subject: 'Welcome to our service, {{customerName}}!',
    content: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Welcome {{customerName}}!</h2>
        <p>Thank you for choosing our service. We're excited to work with {{companyName}}.</p>
        <div style="background-color: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0;">
          <h3>What's next?</h3>
          <ul>
            <li>Complete your profile setup</li>
            <li>Explore our features</li>
            <li>Contact us if you need help</li>
          </ul>
        </div>
        <p>If you have any questions, please don't hesitate to reach out.</p>
        <p>Best regards,<br>Your CRM Team</p>
      </div>
    `,
    variables: ['customerName', 'companyName']
  },
  {
    id: 'followup',
    name: 'Follow-up Email',
    description: 'Follow up after meetings or calls',
    category: 'sales',
    subject: 'Following up on our conversation',
    content: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Hi {{customerName}},</h2>
        <p>I wanted to follow up on our recent conversation regarding {{companyName}}'s needs.</p>
        <div style="background-color: #e8f4fd; padding: 20px; border-radius: 5px; margin: 20px 0;">
          <p><strong>Key points discussed:</strong></p>
          <p>{{customMessage}}</p>
        </div>
        <p>Next steps:</p>
        <ul>
          <li>Review the proposal</li>
          <li>Schedule a demo</li>
          <li>Discuss implementation timeline</li>
        </ul>
        <p>Looking forward to hearing from you.</p>
        <p>Best regards,<br>Your CRM Team</p>
      </div>
    `,
    variables: ['customerName', 'companyName', 'customMessage']
  },
  {
    id: 'proposal',
    name: 'Proposal Email',
    description: 'Send proposals to potential clients',
    category: 'sales',
    subject: 'Proposal for {{companyName}}',
    content: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Dear {{customerName}},</h2>
        <p>Please find attached our proposal for {{companyName}}.</p>
        <div style="background-color: #f0f8f0; padding: 20px; border-radius: 5px; margin: 20px 0;">
          <h3>Proposal Highlights:</h3>
          <p>{{customMessage}}</p>
        </div>
        <p>This proposal includes:</p>
        <ul>
          <li>Detailed service breakdown</li>
          <li>Pricing information</li>
          <li>Implementation timeline</li>
          <li>Support options</li>
        </ul>
        <p>We look forward to discussing this opportunity with you.</p>
        <p>Best regards,<br>Your CRM Team</p>
      </div>
    `,
    variables: ['customerName', 'companyName', 'customMessage']
  },
  {
    id: 'thankyou',
    name: 'Thank You Email',
    description: 'Thank customers after purchase or meeting',
    category: 'customer_service',
    subject: 'Thank you, {{customerName}}!',
    content: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Thank you {{customerName}}!</h2>
        <p>We appreciate your business and trust in our services.</p>
        <div style="background-color: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0;">
          <p><strong>What happens next?</strong></p>
          <p>{{customMessage}}</p>
        </div>
        <p>If you need any assistance, our support team is here to help:</p>
        <ul>
          <li>Email: <EMAIL></li>
          <li>Phone: +****************</li>
          <li>Live Chat: Available 24/7</li>
        </ul>
        <p>Thank you for choosing us!</p>
        <p>Best regards,<br>Your CRM Team</p>
      </div>
    `,
    variables: ['customerName', 'customMessage']
  },
  {
    id: 'reminder',
    name: 'Reminder Email',
    description: 'Send reminders for meetings or deadlines',
    category: 'scheduling',
    subject: 'Reminder: {{eventTitle}}',
    content: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Hi {{customerName}},</h2>
        <p>This is a friendly reminder about our upcoming {{eventTitle}}.</p>
        <div style="background-color: #ffeaa7; padding: 20px; border-radius: 5px; margin: 20px 0;">
          <h3>Event Details:</h3>
          <p><strong>Date:</strong> {{eventDate}}</p>
          <p><strong>Time:</strong> {{eventTime}}</p>
          <p><strong>Duration:</strong> {{eventDuration}}</p>
          <p>{{customMessage}}</p>
        </div>
        <p>Please let us know if you need to reschedule.</p>
        <p>Looking forward to speaking with you!</p>
        <p>Best regards,<br>Your CRM Team</p>
      </div>
    `,
    variables: ['customerName', 'eventTitle', 'eventDate', 'eventTime', 'eventDuration', 'customMessage']
  }
]

export default async function handler(req, res) {
  try {
    // ** Authentication check
    const decoded = authenticateToken(req)
    
    const { id } = req.query

    switch (req.method) {
      case 'GET':
        // ** Get specific template
        const template = emailTemplates.find(t => t.id === id)
        if (!template) {
          return res.status(404).json({ error: 'Template not found' })
        }

        res.status(200).json({ template })
        break

      case 'PUT':
        // ** Update existing template
        const templateIndex = emailTemplates.findIndex(t => t.id === id)
        if (templateIndex === -1) {
          return res.status(404).json({ error: 'Template not found' })
        }

        const updateData = req.body
        const updatedTemplate = {
          ...emailTemplates[templateIndex],
          ...updateData,
          updatedAt: new Date().toISOString()
        }

        emailTemplates[templateIndex] = updatedTemplate

        res.status(200).json({
          message: 'Template updated successfully',
          template: updatedTemplate
        })
        break

      case 'DELETE':
        // ** Delete template
        const deleteIndex = emailTemplates.findIndex(t => t.id === id)
        if (deleteIndex === -1) {
          return res.status(404).json({ error: 'Template not found' })
        }

        emailTemplates.splice(deleteIndex, 1)

        res.status(200).json({
          message: 'Template deleted successfully'
        })
        break

      default:
        res.status(405).json({ message: 'Method not allowed' })
    }

  } catch (error) {
    if (error.name === 'JsonWebTokenError' || error.message === 'No token provided') {
      return res.status(401).json({ error: 'Unauthorized' })
    }
    console.error('Email Template API Error:', error)
    res.status(500).json({ error: 'Internal server error' })
  }
}
