// ** React Imports
import { useState, useEffect } from 'react'

// ** MUI Imports
import Box from '@mui/material/Box'
import Card from '@mui/material/Card'
import Grid from '@mui/material/Grid'
import Button from '@mui/material/Button'
import Typography from '@mui/material/Typography'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import IconButton from '@mui/material/IconButton'
import Chip from '@mui/material/Chip'
import Menu from '@mui/material/Menu'
import MenuItem from '@mui/material/MenuItem'
import Dialog from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import DialogActions from '@mui/material/DialogActions'
import TextField from '@mui/material/TextField'
import FormControl from '@mui/material/FormControl'
import InputLabel from '@mui/material/InputLabel'
import Select from '@mui/material/Select'
import Alert from '@mui/material/Alert'

// ** Icon Imports
import Icon from 'src/@core/components/icon'

// ** Custom Components
import EmailTemplateEditor from 'src/views/crm/email-templates/EmailTemplateEditor'

const EmailTemplatesPage = () => {
  // ** State
  const [templates, setTemplates] = useState([])
  const [loading, setLoading] = useState(true)
  const [selectedTemplate, setSelectedTemplate] = useState(null)
  const [showEditor, setShowEditor] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [templateToDelete, setTemplateToDelete] = useState(null)
  const [anchorEl, setAnchorEl] = useState(null)
  const [menuTemplateId, setMenuTemplateId] = useState(null)
  const [alert, setAlert] = useState(null)

  useEffect(() => {
    fetchTemplates()
  }, [])

  const fetchTemplates = async () => {
    try {
      const token = window.localStorage.getItem('accessToken')
      const response = await fetch('/api/crm/email-templates', {
        headers: {
          Authorization: token
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        setTemplates(data.templates)
      }
    } catch (error) {
      console.error('Error fetching templates:', error)
      setAlert({
        type: 'error',
        message: 'Failed to load templates'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCreateNew = () => {
    setSelectedTemplate(null)
    setShowEditor(true)
  }

  const handleEditTemplate = (template) => {
    setSelectedTemplate(template)
    setShowEditor(true)
    handleCloseMenu()
  }

  const handleDeleteTemplate = async () => {
    if (!templateToDelete) return

    try {
      const token = window.localStorage.getItem('accessToken')
      const response = await fetch(`/api/crm/email-templates/${templateToDelete.id}`, {
        method: 'DELETE',
        headers: {
          Authorization: token
        }
      })
      
      if (response.ok) {
        setAlert({
          type: 'success',
          message: 'Template deleted successfully'
        })
        fetchTemplates()
      } else {
        setAlert({
          type: 'error',
          message: 'Failed to delete template'
        })
      }
    } catch (error) {
      setAlert({
        type: 'error',
        message: 'Network error occurred'
      })
    }

    setShowDeleteDialog(false)
    setTemplateToDelete(null)
  }

  const handleMenuClick = (event, templateId) => {
    setAnchorEl(event.currentTarget)
    setMenuTemplateId(templateId)
  }

  const handleCloseMenu = () => {
    setAnchorEl(null)
    setMenuTemplateId(null)
  }

  const handleTemplateSaved = () => {
    setShowEditor(false)
    setSelectedTemplate(null)
    fetchTemplates()
    setAlert({
      type: 'success',
      message: 'Template saved successfully'
    })
  }

  const getCategoryColor = (category) => {
    const colors = {
      onboarding: 'primary',
      sales: 'success',
      customer_service: 'info',
      scheduling: 'warning',
      marketing: 'secondary'
    }
    return colors[category] || 'default'
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <Typography>Loading templates...</Typography>
      </Box>
    )
  }

  return (
    <Grid container spacing={6}>
      {/* Header */}
      <Grid item xs={12}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant='h4'>Email Templates</Typography>
          <Button
            variant='contained'
            startIcon={<Icon icon='mdi:plus' />}
            onClick={handleCreateNew}
          >
            Create Template
          </Button>
        </Box>
      </Grid>

      {/* Alert */}
      {alert && (
        <Grid item xs={12}>
          <Alert 
            severity={alert.type} 
            onClose={() => setAlert(null)}
            sx={{ mb: 4 }}
          >
            {alert.message}
          </Alert>
        </Grid>
      )}

      {/* Templates Grid */}
      <Grid item xs={12}>
        <Grid container spacing={4}>
          {templates.map((template) => (
            <Grid item xs={12} sm={6} md={4} key={template.id}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <CardHeader
                  title={
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant='h6' noWrap>
                        {template.name}
                      </Typography>
                      <IconButton
                        size='small'
                        onClick={(e) => handleMenuClick(e, template.id)}
                      >
                        <Icon icon='mdi:dots-vertical' />
                      </IconButton>
                    </Box>
                  }
                  subheader={
                    <Chip
                      size='small'
                      label={template.category.replace('_', ' ')}
                      color={getCategoryColor(template.category)}
                      sx={{ textTransform: 'capitalize', mt: 1 }}
                    />
                  }
                />
                <CardContent sx={{ flexGrow: 1 }}>
                  <Typography variant='body2' color='text.secondary' sx={{ mb: 2 }}>
                    {template.description}
                  </Typography>
                  <Typography variant='caption' color='text.secondary'>
                    Subject: {template.subject}
                  </Typography>
                  <Box sx={{ mt: 2 }}>
                    <Typography variant='caption' color='text.secondary'>
                      Variables: {template.variables?.join(', ') || 'None'}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}

          {/* Empty State */}
          {templates.length === 0 && (
            <Grid item xs={12}>
              <Card>
                <CardContent sx={{ textAlign: 'center', py: 8 }}>
                  <Icon icon='mdi:email-outline' fontSize='4rem' color='text.secondary' />
                  <Typography variant='h6' sx={{ mt: 2, mb: 1 }}>
                    No Email Templates
                  </Typography>
                  <Typography variant='body2' color='text.secondary' sx={{ mb: 4 }}>
                    Create your first email template to get started
                  </Typography>
                  <Button
                    variant='contained'
                    startIcon={<Icon icon='mdi:plus' />}
                    onClick={handleCreateNew}
                  >
                    Create Template
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          )}
        </Grid>
      </Grid>

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleCloseMenu}
      >
        <MenuItem
          onClick={() => {
            const template = templates.find(t => t.id === menuTemplateId)
            handleEditTemplate(template)
          }}
        >
          <Icon icon='mdi:pencil' sx={{ mr: 2 }} />
          Edit
        </MenuItem>
        <MenuItem
          onClick={() => {
            const template = templates.find(t => t.id === menuTemplateId)
            setTemplateToDelete(template)
            setShowDeleteDialog(true)
            handleCloseMenu()
          }}
        >
          <Icon icon='mdi:delete' sx={{ mr: 2 }} />
          Delete
        </MenuItem>
      </Menu>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onClose={() => setShowDeleteDialog(false)}>
        <DialogTitle>Delete Template</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete "{templateToDelete?.name}"? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowDeleteDialog(false)}>Cancel</Button>
          <Button onClick={handleDeleteTemplate} color='error' variant='contained'>
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Template Editor Dialog */}
      {showEditor && (
        <EmailTemplateEditor
          template={selectedTemplate}
          open={showEditor}
          onClose={() => {
            setShowEditor(false)
            setSelectedTemplate(null)
          }}
          onSave={handleTemplateSaved}
        />
      )}
    </Grid>
  )
}

export default EmailTemplatesPage
