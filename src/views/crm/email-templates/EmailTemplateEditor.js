// ** React Imports
import { useState, useEffect } from 'react'

// ** MUI Imports
import Box from '@mui/material/Box'
import Card from '@mui/material/Card'
import Grid from '@mui/material/Grid'
import Button from '@mui/material/Button'
import Dialog from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import DialogActions from '@mui/material/DialogActions'
import TextField from '@mui/material/TextField'
import FormControl from '@mui/material/FormControl'
import InputLabel from '@mui/material/InputLabel'
import Select from '@mui/material/Select'
import MenuItem from '@mui/material/MenuItem'
import Typography from '@mui/material/Typography'
import Chip from '@mui/material/Chip'
import IconButton from '@mui/material/IconButton'
import Divider from '@mui/material/Divider'
import Alert from '@mui/material/Alert'
import Tab from '@mui/material/Tab'
import TabContext from '@mui/lab/TabContext'
import TabList from '@mui/lab/TabList'
import TabPanel from '@mui/lab/TabPanel'

// ** Third Party Imports
import { EditorState, convertToRaw, ContentState } from 'draft-js'

// ** Icon Imports
import Icon from 'src/@core/components/icon'

// ** Custom Components
import ReactDraftWysiwyg from 'src/@core/components/react-draft-wysiwyg'

// ** Styled Component Imports
import { EditorWrapper } from 'src/@core/styles/libs/react-draft-wysiwyg'

// ** Styles
import 'react-draft-wysiwyg/dist/react-draft-wysiwyg.css'

const EmailTemplateEditor = ({ template, open, onClose, onSave }) => {
  // ** State
  const [activeTab, setActiveTab] = useState('editor')
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    category: '',
    subject: '',
    variables: []
  })
  const [editorState, setEditorState] = useState(EditorState.createEmpty())
  const [newVariable, setNewVariable] = useState('')
  const [loading, setSaving] = useState(false)
  const [alert, setAlert] = useState(null)
  const [previewContent, setPreviewContent] = useState('')

  // ** Categories
  const categories = [
    { value: 'onboarding', label: 'Onboarding' },
    { value: 'sales', label: 'Sales' },
    { value: 'customer_service', label: 'Customer Service' },
    { value: 'scheduling', label: 'Scheduling' },
    { value: 'marketing', label: 'Marketing' }
  ]

  // ** Common variables
  const commonVariables = [
    'customerName',
    'companyName',
    'customMessage',
    'eventTitle',
    'eventDate',
    'eventTime',
    'eventDuration'
  ]

  useEffect(() => {
    if (template) {
      // ** Editing existing template
      setFormData({
        name: template.name || '',
        description: template.description || '',
        category: template.category || '',
        subject: template.subject || '',
        variables: template.variables || []
      })

      // ** Set editor content
      if (template.content && typeof window !== 'undefined') {
        import('html-to-draftjs').then(htmlToDraftModule => {
          const htmlToDraftFunc = htmlToDraftModule.default
          const contentBlock = htmlToDraftFunc(template.content)
          if (contentBlock) {
            const contentState = ContentState.createFromBlockArray(contentBlock.contentBlocks)
            setEditorState(EditorState.createWithContent(contentState))
          }
        })
      }
    } else {
      // ** Creating new template
      setFormData({
        name: '',
        description: '',
        category: '',
        subject: '',
        variables: []
      })
      setEditorState(EditorState.createEmpty())
    }
  }, [template])

  const handleInputChange = field => event => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value
    }))
  }

  const handleAddVariable = () => {
    if (newVariable && !formData.variables.includes(newVariable)) {
      setFormData(prev => ({
        ...prev,
        variables: [...prev.variables, newVariable]
      }))
      setNewVariable('')
    }
  }

  const handleRemoveVariable = variableToRemove => {
    setFormData(prev => ({
      ...prev,
      variables: prev.variables.filter(v => v !== variableToRemove)
    }))
  }

  const handleAddCommonVariable = variable => {
    if (!formData.variables.includes(variable)) {
      setFormData(prev => ({
        ...prev,
        variables: [...prev.variables, variable]
      }))
    }
  }

  const insertVariable = variable => {
    const currentContent = editorState.getCurrentContent()
    const currentSelection = editorState.getSelection()

    // ** Insert variable placeholder
    const variableText = `{{${variable}}}`
    const newContent = currentContent.createEntity('VARIABLE', 'IMMUTABLE', { variable })
    const entityKey = newContent.getLastCreatedEntityKey()

    // ** For simplicity, we'll just insert as plain text
    const newEditorState = EditorState.createWithContent(
      ContentState.createFromText(
        currentContent.getPlainText().slice(0, currentSelection.getStartOffset()) +
          variableText +
          currentContent.getPlainText().slice(currentSelection.getEndOffset())
      )
    )

    setEditorState(newEditorState)
  }

  const updatePreviewContent = async () => {
    if (typeof window === 'undefined') return

    try {
      const rawContent = convertToRaw(editorState.getCurrentContent())

      // ** Use dynamic import for draftToHtml
      const draftToHtmlModule = await import('draftjs-to-html')
      const draftToHtmlFunc = draftToHtmlModule.default
      let htmlContent = draftToHtmlFunc(rawContent)

      // ** Replace variables with sample data for preview
      const sampleData = {
        customerName: 'John Doe',
        companyName: 'Acme Corp',
        customMessage: 'This is a sample custom message',
        eventTitle: 'Product Demo',
        eventDate: 'March 15, 2024',
        eventTime: '2:00 PM',
        eventDuration: '1 hour'
      }

      formData.variables.forEach(variable => {
        const regex = new RegExp(`{{${variable}}}`, 'g')
        htmlContent = htmlContent.replace(regex, sampleData[variable] || `[${variable}]`)
      })

      setPreviewContent(htmlContent)
    } catch (error) {
      console.error('Error updating preview:', error)
      setPreviewContent('<p>Error generating preview</p>')
    }
  }

  // ** Update preview when editor content or variables change
  useEffect(() => {
    updatePreviewContent()
  }, [editorState, formData.variables])

  const handleSave = async () => {
    try {
      setSaving(true)
      setAlert(null)

      // ** Validation
      if (!formData.name || !formData.category || !formData.subject) {
        setAlert({
          type: 'error',
          message: 'Please fill in all required fields'
        })
        return
      }

      // ** Get HTML content from editor
      const rawContent = convertToRaw(editorState.getCurrentContent())

      // ** Use dynamic import for draftToHtml
      const draftToHtmlModule = await import('draftjs-to-html')
      const draftToHtmlFunc = draftToHtmlModule.default
      const htmlContent = draftToHtmlFunc(rawContent)

      const templateData = {
        ...formData,
        content: htmlContent
      }

      const token = window.localStorage.getItem('accessToken')
      const url = template ? `/api/crm/email-templates/${template.id}` : '/api/crm/emails/templates'

      const method = template ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          Authorization: token
        },
        body: JSON.stringify(templateData)
      })

      if (response.ok) {
        onSave()
      } else {
        const errorData = await response.json()
        setAlert({
          type: 'error',
          message: errorData.message || 'Failed to save template'
        })
      }
    } catch (error) {
      console.error('Error saving template:', error)
      setAlert({
        type: 'error',
        message: 'Network error occurred'
      })
    } finally {
      setSaving(false)
    }
  }

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth='lg'
      fullWidth
      PaperProps={{
        sx: { height: '90vh' }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant='h6'>{template ? 'Edit Email Template' : 'Create Email Template'}</Typography>
          <IconButton onClick={onClose}>
            <Icon icon='mdi:close' />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ p: 0 }}>
        {alert && (
          <Alert severity={alert.type} onClose={() => setAlert(null)} sx={{ m: 4, mb: 0 }}>
            {alert.message}
          </Alert>
        )}

        <TabContext value={activeTab}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider', px: 4 }}>
            <TabList onChange={(e, newValue) => setActiveTab(newValue)}>
              <Tab label='Template Editor' value='editor' />
              <Tab label='Preview' value='preview' />
            </TabList>
          </Box>

          <TabPanel value='editor' sx={{ p: 4 }}>
            <Grid container spacing={4}>
              {/* Template Metadata */}
              <Grid item xs={12} md={4}>
                <Card sx={{ p: 3, height: 'fit-content' }}>
                  <Typography variant='h6' sx={{ mb: 3 }}>
                    Template Details
                  </Typography>

                  <TextField
                    fullWidth
                    label='Template Name'
                    value={formData.name}
                    onChange={handleInputChange('name')}
                    required
                    sx={{ mb: 3 }}
                  />

                  <TextField
                    fullWidth
                    label='Description'
                    value={formData.description}
                    onChange={handleInputChange('description')}
                    multiline
                    rows={3}
                    sx={{ mb: 3 }}
                  />

                  <FormControl fullWidth sx={{ mb: 3 }}>
                    <InputLabel>Category</InputLabel>
                    <Select
                      value={formData.category}
                      onChange={handleInputChange('category')}
                      label='Category'
                      required
                    >
                      {categories.map(category => (
                        <MenuItem key={category.value} value={category.value}>
                          {category.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>

                  <TextField
                    fullWidth
                    label='Email Subject'
                    value={formData.subject}
                    onChange={handleInputChange('subject')}
                    required
                    sx={{ mb: 3 }}
                  />

                  <Divider sx={{ my: 3 }} />

                  {/* Variables Section */}
                  <Typography variant='h6' sx={{ mb: 2 }}>
                    Variables
                  </Typography>

                  <Box sx={{ mb: 2 }}>
                    <TextField
                      size='small'
                      label='Add Variable'
                      value={newVariable}
                      onChange={e => setNewVariable(e.target.value)}
                      onKeyPress={e => {
                        if (e.key === 'Enter') {
                          handleAddVariable()
                        }
                      }}
                      InputProps={{
                        endAdornment: (
                          <IconButton size='small' onClick={handleAddVariable}>
                            <Icon icon='mdi:plus' />
                          </IconButton>
                        )
                      }}
                    />
                  </Box>

                  <Typography variant='body2' color='text.secondary' sx={{ mb: 2 }}>
                    Common Variables:
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                    {commonVariables.map(variable => (
                      <Chip
                        key={variable}
                        label={variable}
                        size='small'
                        onClick={() => handleAddCommonVariable(variable)}
                        sx={{ cursor: 'pointer' }}
                      />
                    ))}
                  </Box>

                  <Typography variant='body2' color='text.secondary' sx={{ mb: 1 }}>
                    Template Variables:
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {formData.variables.map(variable => (
                      <Chip
                        key={variable}
                        label={`{{${variable}}}`}
                        onDelete={() => handleRemoveVariable(variable)}
                        onClick={() => insertVariable(variable)}
                        color='primary'
                        size='small'
                        sx={{ cursor: 'pointer' }}
                      />
                    ))}
                  </Box>
                </Card>
              </Grid>

              {/* Editor */}
              <Grid item xs={12} md={8}>
                <Card sx={{ p: 3 }}>
                  <Typography variant='h6' sx={{ mb: 3 }}>
                    Email Content
                  </Typography>

                  <EditorWrapper>
                    <ReactDraftWysiwyg
                      editorState={editorState}
                      onEditorStateChange={setEditorState}
                      placeholder='Enter your email content here...'
                      editorStyle={{
                        minHeight: '400px',
                        border: '1px solid #ddd',
                        padding: '10px'
                      }}
                      toolbar={{
                        options: [
                          'inline',
                          'blockType',
                          'fontSize',
                          'list',
                          'textAlign',
                          'colorPicker',
                          'link',
                          'image'
                        ],
                        inline: {
                          inDropdown: false,
                          options: ['bold', 'italic', 'underline', 'strikethrough']
                        },
                        blockType: {
                          inDropdown: true,
                          options: ['Normal', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6', 'Blockquote']
                        },
                        fontSize: {
                          options: [8, 9, 10, 11, 12, 14, 16, 18, 24, 30, 36, 48, 60, 72, 96]
                        },
                        list: {
                          inDropdown: false,
                          options: ['unordered', 'ordered']
                        },
                        textAlign: {
                          inDropdown: false,
                          options: ['left', 'center', 'right', 'justify']
                        }
                      }}
                    />
                  </EditorWrapper>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>

          <TabPanel value='preview' sx={{ p: 4 }}>
            <Card sx={{ p: 3 }}>
              <Typography variant='h6' sx={{ mb: 3 }}>
                Email Preview
              </Typography>

              <Box sx={{ mb: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                <Typography variant='body2' color='text.secondary'>
                  <strong>Subject:</strong>{' '}
                  {formData.subject.replace(/{{(\w+)}}/g, (match, variable) => {
                    const sampleData = {
                      customerName: 'John Doe',
                      companyName: 'Acme Corp',
                      eventTitle: 'Product Demo'
                    }
                    return sampleData[variable] || `[${variable}]`
                  })}
                </Typography>
              </Box>

              <Box
                sx={{
                  border: '1px solid #ddd',
                  borderRadius: 1,
                  p: 3,
                  bgcolor: 'background.paper',
                  minHeight: '400px'
                }}
                dangerouslySetInnerHTML={{ __html: previewContent }}
              />
            </Card>
          </TabPanel>
        </TabContext>
      </DialogContent>

      <DialogActions sx={{ p: 4 }}>
        <Button onClick={onClose}>Cancel</Button>
        <Button
          variant='contained'
          onClick={handleSave}
          disabled={loading}
          startIcon={loading ? <Icon icon='mdi:loading' className='animate-spin' /> : <Icon icon='mdi:content-save' />}
        >
          {loading ? 'Saving...' : 'Save Template'}
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default EmailTemplateEditor
